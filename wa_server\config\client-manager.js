const { Client, LocalAuth, MessageMedia } = require('whatsapp-web.js');
const qrcode = require('qrcode');
const path = require('path');
const fs = require('fs');
const config = require('./config');

class WhatsAppClientManager {
    constructor(apiService = null) {
        this.clients = new Map(); // userId -> client instance
        this.clientStates = new Map(); // userId -> state info
        this.api = apiService; // Use provided API service

        // Ensure directories exist
        this.ensureDirectories();

        // Start cleanup for inactive QR sessions
        this.startInactiveCleanup();
    }

    ensureDirectories() {
        Object.values(config.paths).forEach(dirPath => {
            if (!fs.existsSync(dirPath)) {
                fs.mkdirSync(dirPath, { recursive: true });
            }
        });
    }

    // Auto-reconnect existing sessions on server startup
    async autoReconnectExistingSessions() {
        console.log('🔍 Scanning for existing WhatsApp sessions...');

        try {
            const sessionsDir = config.paths.sessions;
            if (!fs.existsSync(sessionsDir)) {
                console.log('📁 No sessions directory found');
                return;
            }

            const sessionFolders = fs.readdirSync(sessionsDir)
                .filter(folder => {
                    const folderPath = path.join(sessionsDir, folder);
                    return fs.statSync(folderPath).isDirectory() && folder.startsWith('user_');
                });

            if (sessionFolders.length === 0) {
                console.log('📭 No existing sessions found');
                return;
            }

            console.log(`🔄 Found ${sessionFolders.length} existing session(s): ${sessionFolders.join(', ')}`);
            console.log('🧹 Checking sessions - will only clean up non-connected ones...');

            // Only clean up non-connected sessions - preserve connected users
            for (let i = 0; i < sessionFolders.length; i++) {
                const folder = sessionFolders[i];
                const userId = folder.replace('user_', '');

                // Add delay between checks to avoid overwhelming the system
                setTimeout(async () => {
                    try {
                        // Check if user has a connected state
                        const state = this.clientStates.get(userId);

                        // DEBUG: Show user status for debugging
                        console.log(`🔍 DEBUG - User ${userId} status:`, {
                            hasState: !!state,
                            status: state?.status || 'NO_STATE',
                            lastActivity: state?.lastActivity ? new Date(state.lastActivity).toISOString() : 'NO_ACTIVITY',
                            clientExists: this.clients.has(userId)
                        });

                        // FIXED: On server startup, clientStates is empty, so preserve all sessions
                        // Only the inactive cleanup (45 second timeout) should remove sessions
                        console.log(`✅ Preserving session for user ${userId} (startup - clientStates is empty on restart)`);

                        // if (!state || state.status !== 'connected') {
                        //     console.log(`🗑️ Cleaning up non-connected session for user ${userId}...`);
                        //     await this.cleanupSessionFiles(userId);
                        //     console.log(`✅ Cleaned up non-connected session for user ${userId}`);
                        // } else {
                        //     console.log(`✅ Preserving connected session for user ${userId}`);
                        // }
                    } catch (error) {
                        console.error(`❌ Failed to check/cleanup session for user ${userId}:`, error.message);
                    }
                }, i * 1000); // 1 second delay between each check
            }

            console.log('🎯 Session check initiated - only non-connected sessions will be cleaned');

        } catch (error) {
            console.error('❌ Error during session cleanup:', error);
        }
    }

    // Get or create client for user
    async getClient(userId) {
        console.log(`📋 Getting client for user ${userId}`);

        if (this.clients.has(userId)) {
            const existingClient = this.clients.get(userId);
            const state = this.clientStates.get(userId);

            // Check if client is in a valid state
            if (state && (state.status === 'connected' || state.status === 'ready')) {
                console.log(`♻️ Returning existing client for user ${userId} (status: ${state.status})`);
                return existingClient;
            } else {
                // Client exists but is in bad state, clean it up and create new one
                console.log(`🔄 Existing client for user ${userId} is in bad state (${state?.status || 'unknown'}), cleaning up...`);
                await this.cleanupInactiveSession(userId);
            }
        }

        // Check if we've reached max clients
        if (this.clients.size >= config.whatsapp.maxClients) {
            throw new Error('Maximum client limit reached');
        }

        console.log(`🆕 Creating new WhatsApp client for user ${userId}`);
        return this.createClient(userId);
    }

    async createClient(userId) {
        const client = new Client({
            authStrategy: new LocalAuth({
                clientId: `user_${userId}`,
                dataPath: path.join(config.paths.sessions, `user_${userId}`)
            }),
            puppeteer: {
                headless: true,
                args: [
                    '--no-sandbox',
                    '--disable-setuid-sandbox',
                    '--disable-dev-shm-usage',
                    '--disable-accelerated-2d-canvas',
                    '--no-first-run',
                    '--no-zygote',
                    '--single-process',
                    '--disable-gpu'
                ],
                timeout: config.whatsapp.puppeteerTimeout
            }
        });

        // Initialize fresh client state
        this.clientStates.set(userId, {
            status: 'connecting',
            qrCode: null,
            clientInfo: null,
            lastActivity: Date.now()
        });

        // Notify API about connecting status
        this.api?.notifyAuth(userId, 'connecting', 'Connecting to WhatsApp, please wait...');

        // Set up event listeners
        this.setupClientEvents(client, userId);

        // Store client
        this.clients.set(userId, client);
        console.log(`💾 Client stored for user ${userId}`);

        // Initialize client
        console.log(`🔄 Initializing WhatsApp client for user ${userId}...`);
        try {
            await client.initialize();
            console.log(`✅ WhatsApp client initialized for user ${userId}`);
        } catch (error) {
            console.error(`❌ Failed to initialize client for user ${userId}:`, error.message);
            // Clean up failed client
            this.clients.delete(userId);
            this.clientStates.delete(userId);
            throw error;
        }

        return client;
    }

    setupClientEvents(client, userId) {
        // QR Code generation
        client.on('qr', (qr) => {
            console.log(`📱 QR generated for user ${userId}`);
            qrcode.toDataURL(qr, (err, url) => {
                if (!err) {
                    this.updateClientState(userId, {
                        status: 'ready',
                        qrCode: url
                    });
                    // Send ready status with QR code
                    this.api?.notifyAuth(userId, 'ready', url);
                } else {
                    console.error(`❌ Error generating QR for user ${userId}:`, err);
                }
            });
        });

        // Client ready
        client.on('ready', async () => {
            console.log(`📱 WhatsApp ready for user ${userId}`);

            try {
                const clientInfo = client.info;
                let profilePicUrl = null;

                try {
                    if (this.clients.has(userId) && client.info && client.info.wid) {
                        profilePicUrl = await client.getProfilePicUrl(clientInfo.wid._serialized);
                    }
                } catch (error) {
                    // Silently handle profile picture errors
                }

                const fullClientInfo = { ...clientInfo, profilePicUrl };

                this.updateClientState(userId, {
                    status: 'connected',
                    qrCode: null,
                    clientInfo: fullClientInfo
                });

                // Send ready status with client info
                this.api?.notifyAuth(userId, 'ready', fullClientInfo);
            } catch (error) {
                console.error(`❌ Error in ready event for user ${userId}:`, error);
            }
        });

        // Authentication events
        client.on('authenticated', () => {
            console.log(`WhatsApp authenticated for user ${userId}`);
            this.updateClientState(userId, {
                status: 'authenticated',
                qrCode: null,
                clientInfo: client.info
            });
            // Send authenticated status with client info
            this.api?.notifyAuth(userId, 'authenticated', client.info);
        });

        client.on('auth_failure', (msg) => {
            console.error(`Authentication failure for user ${userId}:`, msg);
            this.updateClientState(userId, {
                status: 'connecting',
                qrCode: null,
                clientInfo: null
            });
            // Send connecting status to retry connection
            this.api?.notifyAuth(userId, 'connecting', 'Authentication failed, retrying connection...');
        });

        // Disconnection handling
        client.on('disconnected', async (reason) => {
            console.log(`WhatsApp disconnected for user ${userId}:`, reason);

            if (reason === 'LOGOUT') {
                await this.handleLogout(userId, client);
                return;
            }

            this.updateClientState(userId, {
                status: 'connecting',
                qrCode: null,
                clientInfo: null
            });

            // Send connecting status to indicate reconnection attempt
            this.api?.notifyAuth(userId, 'connecting', `Disconnected: ${reason}. Attempting to reconnect...`);
        });

        // Loading screen
        let loadingScreenTriggered = false;
        client.on('loading_screen', (percent, message) => {
            if (!loadingScreenTriggered) {
                loadingScreenTriggered = true;
                this.updateClientState(userId, {
                    status: 'authenticating',
                    qrCode: null
                });
                // Send authenticating status with message
                this.api?.notifyAuth(userId, 'authenticating', `${message} (${percent}%)`);
            }
        });

        // Message handling
        client.on('message', async (message) => {
            try {
                await this.handleIncomingMessage(userId, message);
            } catch (error) {
                console.error(`❌ Error handling message for user ${userId}:`, error);
            }
        });

        client.on('message_ack', async (message, ack) => {
            try {
                await this.handleMessageAck(userId, message, ack);
            } catch (error) {
                console.error(`❌ Error handling message ack for user ${userId}:`, error);
            }
        });

        // Removed message_ack handler for speed - not essential for basic messaging
    }

    async handleLogout(userId, client) {
        console.log(`🧹 User ${userId} logged out from device - handling cleanup safely`);

        try {
            this.clients.delete(userId);
            this.clientStates.delete(userId);

            try {
                await client.destroy();
            } catch (destroyError) {
                console.log(`⚠️ Client destroy error (expected after device logout) for user ${userId}`);
            }

            setTimeout(async () => {
                try {
                    await this.cleanupSessionFiles(userId);
                } catch (cleanupError) {
                    setTimeout(async () => {
                        try {
                            await this.cleanupSessionFiles(userId);
                        } catch (retryError) {
                            console.log(`⚠️ Session cleanup failed on retry for user ${userId}`);
                        }
                    }, 5000);
                }
            }, config.whatsapp.cleanupDelay);

            // Send unauthenticated status for logout
            this.api?.notifyAuth(userId, 'unauthenticated', 'Logged out from device');

            // Auto-reconnect regardless of socket status to maintain message reception
            setTimeout(async () => {
                try {
                    console.log(`🔄 Auto-reconnecting WhatsApp client for user ${userId} after device logout`);
                    await this.getClient(userId);
                    console.log(`✅ WhatsApp client auto-reconnected for user ${userId}`);
                } catch (error) {
                    console.error(`❌ Error auto-reconnecting client for user ${userId}:`, error);
                }
            }, config.whatsapp.autoReconnectDelay);

        } catch (error) {
            console.error(`❌ Error handling device logout for user ${userId}:`, error);
        }
    }

    // Handle incoming messages - PRIVATE CHATS ONLY
    async handleIncomingMessage(userId, message) {
        try {
            // Skip status/broadcast messages (private chats only)
            if (message.isStatus || message.broadcast || message.from.includes('status@broadcast') || message.from.includes('@g.us')) {
                return; // Skip groups and status silently
            }

            if (!this.api) {
                console.log('📝 API service disabled - skipping message processing');
                return;
            }

            // Prevent duplicate processing - check if we've already processed this message
            const messageKey = `${userId}_${message.id._serialized}`;
            if (!this.processedMessages) {
                this.processedMessages = new Set();
            }

            if (this.processedMessages.has(messageKey)) {
                console.log(`⚠️ Skipping duplicate message: ${message.id._serialized}`);
                return;
            }

            // Mark message as being processed
            this.processedMessages.add(messageKey);

            // Clean up old processed messages (keep only last 1000)
            if (this.processedMessages.size > 1000) {
                const oldMessages = Array.from(this.processedMessages).slice(0, 500);
                oldMessages.forEach(msg => this.processedMessages.delete(msg));
            }

            // Prepare raw message data for Laravel - MINIMAL data extraction
            const rawMessageData = {
                user_id: userId, // Add the user ID so Laravel knows which user this belongs to
                whatsapp_message_id: message.id._serialized,
                whatsapp_contact_id: message.from,
                message_body: message.body || null,
                message_type: message.type,
                is_outgoing: message.fromMe,
                timestamp: message.timestamp,
                has_media: message.hasMedia
            };

            // Get contact/chat info SYNCHRONOUSLY (wait for it)
            try {
                console.log(`🔍 Processing message from: ${message.from} (${message.type})`);
                const [contact, chat] = await Promise.all([
                    message.getContact().catch(() => null),
                    message.getChat().catch(() => null)
                ]);

                if (contact) {
                    rawMessageData.contact_number = contact.number;
                    rawMessageData.contact_name = contact.name || contact.pushname || contact.number;
                    rawMessageData.contact_is_business = contact.isBusiness || false;

                    // Get profile picture URL
                    try {
                        const client = this.clients.get(userId);
                        if (client && contact.id && contact.id._serialized) {
                            const profilePicUrl = await client.getProfilePicUrl(contact.id._serialized);
                            if (profilePicUrl) {
                                rawMessageData.contact_profile_picture = profilePicUrl;
                            }
                        }
                    } catch (profilePicError) {
                        // Silently handle profile picture errors - not critical
                        console.log(`⚠️ Could not get profile picture for ${contact.number || 'unknown'}`);
                    }
                }

                if (chat) {
                    rawMessageData.chat_id = chat.id._serialized;
                    rawMessageData.chat_name = chat.name;
                    rawMessageData.is_group = chat.isGroup;
                }
            } catch (contactError) {
                console.error(`❌ Error getting contact/chat info:`, contactError);
                // Continue with basic data
            }

            // Add media data if present - we need to download to get a proper URL
            if (message.hasMedia) {
                try {
                    console.log(`📎 Processing media for message type: ${message.type}`);

                    // Download media to get the actual data
                    const media = await message.downloadMedia();
                    if (media) {
                        // Convert to base64 and send to Laravel for storage
                        rawMessageData.media_data = media.data;
                        rawMessageData.media_mimetype = media.mimetype;
                        rawMessageData.media_filename = media.filename || `media_${Date.now()}`;

                        console.log(`✅ Media downloaded: ${media.mimetype}, size: ${media.data?.length || 0} bytes`);
                    } else {
                        console.log(`⚠️ No media data returned`);
                    }
                } catch (error) {
                    console.error(`❌ Error downloading media:`, error);
                }
            }

            // Debug: Log what we're sending to Laravel
            console.log(`📤 Sending to Laravel:`, {
                message_id: rawMessageData.whatsapp_message_id,
                contact_id: rawMessageData.whatsapp_contact_id,
                type: rawMessageData.message_type,
                has_media: rawMessageData.has_media,
                media_data_size: rawMessageData.media_data ? rawMessageData.media_data.length : 0,
                media_mimetype: rawMessageData.media_mimetype
            });

            // Send everything to Laravel - Laravel does ALL the work
            const result = await this.api.processIncomingMessage(rawMessageData);

            // No need to emit - Laravel will handle notifications via Reverb

        } catch (error) {
            console.error(`❌ Error handling incoming message for user ${userId}:`, error);
        }
    }

    async handleMessageAck(userId, message, ack) {
        try {
            if (!this.api) {
                console.log('📝 API service disabled - skipping message ack processing');
                return;
            }
            console.log(`🔄 Handling message ack for: ${message.id._serialized} (ack: ${ack})`);
            // Send ack update to Laravel
            const result = await this.api.updateMessageAck({
                message_id: message.id._serialized,
                ack: ack
            });

        } catch (error) {
            console.error(`❌ Error handling message ack for user ${userId}:`, error);
        }
    }

    // Removed handleMessageAck for speed - not essential for basic messaging

    getMessageType(message) {
        const typeMap = {
            'chat': 'text',
            'image': 'image',
            'video': 'video',
            'audio': 'audio',
            'ptt': 'audio',
            'document': 'document',
            'location': 'location',
            'vcard': 'contact'
        };
        return typeMap[message.type] || 'text';
    }

    getMimeTypeFromMessageType(messageType) {
        const mimeMap = {
            'image': 'image/jpeg',
            'video': 'video/mp4',
            'audio': 'audio/mpeg',
            'ptt': 'audio/ogg',
            'document': 'application/octet-stream'
        };
        return mimeMap[messageType] || 'application/octet-stream';
    }

    // REMOVED: storeAttachment - Laravel handles everything now

    // State management
    updateClientState(userId, updates) {
        const currentState = this.clientStates.get(userId) || {};
        const newState = {
            ...currentState,
            ...updates,
            lastActivity: Date.now()
        };

        this.clientStates.set(userId, newState);

        // Status changes are now handled by individual event handlers
        if (updates.status && updates.status !== currentState.status) {
            console.log(`📊 Status changed for user ${userId}: ${currentState.status} → ${updates.status}`);
        }
    }

    getClientState(userId) {
        return this.clientStates.get(userId) || {
            status: 'unauthenticated',
            qrCode: null,
            clientInfo: null
        };
    }

    // Send message method for API requests
    async sendMessage(userId, chatId, message, type = 'text', media = null) {
        try {
            const client = this.clients.get(userId);
            if (!client) {
                throw new Error('WhatsApp client not connected');
            }

            let sentMessage;
            const { MessageMedia } = require('whatsapp-web.js');

            if (type === 'text') {
                sentMessage = await client.sendMessage(chatId, message);
            } else if (type === 'media' && media) {
                const mediaMessage = new MessageMedia(media.mimetype, media.data, media.filename);
                sentMessage = await client.sendMessage(chatId, mediaMessage, { caption: message });
            }

            if (sentMessage) {
                console.log(`✅ Message sent: ${sentMessage.id._serialized}`);
                return { success: true, messageId: sentMessage.id._serialized };
            }

            return { success: false, error: 'Failed to send message' };
        } catch (error) {
            console.error('❌ Error sending message:', error);
            return { success: false, error: error.message };
        }
    }

    // Cleanup methods
    async cleanupSessionFiles(userId) {
        const sessionDir = path.join(config.paths.sessions, `user_${userId}`);
        try {
            if (fs.existsSync(sessionDir)) {
                console.log(`🗑️ Removing session directory for user ${userId}`);
                await fs.promises.rm(sessionDir, { recursive: true, force: true });
                console.log(`✅ Session directory removed for user ${userId}`);
            }
        } catch (error) {
            console.error(`❌ Error removing session directory for user ${userId}:`, error.message);
        }
    }

    async disconnectClient(userId) {
        const client = this.clients.get(userId);
        if (client) {
            try {
                await client.destroy();
                console.log(`Client disconnected for user ${userId}`);
            } catch (error) {
                console.error(`Error disconnecting client for user ${userId}:`, error);
            }
            this.clients.delete(userId);
            this.clientStates.delete(userId);
        }
    }

    // Simple cleanup for inactive QR sessions
    startInactiveCleanup() {
        setInterval(async () => {
            try {
                const now = Date.now();
                const timeout = 45 * 1000; // 45 seconds (since heartbeat is every 30 seconds)

                for (const [userId, state] of this.clientStates.entries()) {
                    try {
                        // Only cleanup non-authenticated sessions that are inactive
                        if (state.status !== 'connected' &&
                            (now - state.lastActivity) > timeout) {

                            console.log(`🗑️ Cleaning up inactive QR session for user ${userId}`);
                            await this.cleanupInactiveSession(userId);
                        }
                    } catch (error) {
                        console.error(`❌ Error in cleanup loop for user ${userId}:`, error.message);
                        // Continue with other users
                    }
                }
            } catch (error) {
                console.error(`❌ Error in cleanup interval:`, error.message);
            }
        }, 30 * 1000); // Check every 30 seconds
    }

    async cleanupInactiveSession(userId) {
        try {
            const client = this.clients.get(userId);
            if (client) {
                console.log(`🗑️ Destroying client for user ${userId}...`);

                // Add timeout to prevent hanging
                const destroyPromise = client.destroy();
                const timeoutPromise = new Promise((_, reject) =>
                    setTimeout(() => reject(new Error('Destroy timeout')), 10000)
                );

                try {
                    await Promise.race([destroyPromise, timeoutPromise]);
                    console.log(`✅ Client destroyed for user ${userId}`);
                } catch (destroyError) {
                    console.log(`⚠️ Client destroy failed for user ${userId}:`, destroyError.message);
                    // Continue with cleanup anyway
                }
            }

            this.clients.delete(userId);
            this.clientStates.delete(userId);
            console.log(`🧹 Session cleaned up for user ${userId}`);

        } catch (error) {
            console.error(`❌ Error cleaning up session for ${userId}:`, error.message);
            // Force cleanup even if destroy fails
            this.clients.delete(userId);
            this.clientStates.delete(userId);
        }
    }

    // Stats
    getStats() {
        return {
            totalClients: this.clients.size,
            maxClients: config.whatsapp.maxClients
        };
    }
}

module.exports = WhatsAppClientManager;
